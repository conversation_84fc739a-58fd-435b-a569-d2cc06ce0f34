# Laravel specific
.env
.env.example
.env.prod
.env.local
.env.*.php
.env.php
.env.development.local
.env.test.local
.env.production.local

# Laravel storage and public directories
/storage/*.key
/storage/debugbar
/public/storage
/public/hot
/public/uploads
/public/assets
/public/logs
/public/css
/public/js
/public/mix-manifest.json
/public/app-translations
public/uploads.zip
public/all

# Laravel 4 specific
/bootstrap/compiled.php
app/storage/

# Laravel 5 & Lumen specific with changed public path
public_html/storage
public_html/hot

# Dependencies
/vendor
/node_modules
/jspm_packages
/bower_components
/web_modules

# Lock files should be committed to ensure consistent dependencies
# composer.lock
# pnpm-lock.yaml

# IDE and editor files
/.idea
/.vscode
/.vagrant
.junie
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/**/usage.statistics.xml
.idea/**/dictionaries
.idea/**/shelf
.idea/**/aws.xml
.idea/**/contentModel.xml
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml
.idea/**/dbnavigator.xml
.idea/**/gradle.xml
.idea/**/libraries
.idea/replstate.xml
.idea/sonarlint/
.idea/httpRequests
.idea/caches/build_file_checksums.ser
.idea_modules/
*.iws
out/
/nbproject
/nbproject/private/

# Testing
.phpunit.result.cache
.phpunit.cache
/app/phpunit.xml
/phpunit.xml
/build/
coverage
*.lcov
.nyc_output

# Logs and debugging
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Build and temp directories
/temp
/build
/dist
/.next
/.nuxt
/.vuepress/dist
/.temp
/.cache
/.parcel-cache
/.docusaurus
/.serverless/
/.fusebox/
/.dynamodb/
out
/docs
/public

# Runtime data
pids
*.pid
*.seed
*.pid.lock
.lock-wscript
.tern-port

# Caches
.npm
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/
.node_repl_history
*.tsbuildinfo
.yarn-integrity
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
cmake-build-*/

# OS specific
.DS_Store

# Project specific
Homestead.json
Homestead.yaml
updates/
updates.zip
ecommerce.zip
install.zip
resources/lang/en.json

# Misc
*.tgz
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties
atlassian-ide-plugin.xml
