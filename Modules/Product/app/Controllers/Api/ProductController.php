<?php

declare(strict_types=1);

namespace Modules\Product\app\Controllers\Api;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Product\app\Http\Requests\Api\ProductRequest;
use Modules\Product\app\Http\Resources\ProductCollection;
use Modules\Product\app\Http\Resources\ProductResource;
use Modules\Product\app\Models\Product;
use Modules\Product\app\Services\ProductService;

class ProductController extends Controller
{
    /**
     * The product service instance.
     *
     * @var ProductService
     */
    protected ProductService $productService;

    /**
     * Create a new controller instance.
     *
     * @param ProductService $productService
     */
    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $products = Product::query()
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhereHas('stocks', function ($q) use ($search) {
                        $q->where('sku', 'like', '%' . $search . '%');
                    });
            })
            ->when($request->category_id, function ($query, $category_id) {
                return $query->whereHas('categories', function ($q) use ($category_id) {
                    $q->where('categories.id', $category_id);
                });
            })
            ->when($request->brand_id, function ($query, $brand_id) {
                return $query->where('brand_id', $brand_id);
            })
            ->when($request->min_price, function ($query, $min_price) {
                return $query->where('unit_price', '>=', $min_price);
            })
            ->when($request->max_price, function ($query, $max_price) {
                return $query->where('unit_price', '<=', $max_price);
            })
            ->when($request->sort_by, function ($query, $sort_by) {
                if ($sort_by === 'price_low_to_high') {
                    return $query->orderBy('unit_price', 'asc');
                } elseif ($sort_by === 'price_high_to_low') {
                    return $query->orderBy('unit_price', 'desc');
                } elseif ($sort_by === 'newest') {
                    return $query->orderBy('created_at', 'desc');
                } elseif ($sort_by === 'oldest') {
                    return $query->orderBy('created_at', 'asc');
                }
                return $query;
            }, function ($query) {
                return $query->orderBy('created_at', 'desc');
            })
            ->where('published', 1)
            ->where('approved', 1)
            ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => new ProductCollection($products),
            'message' => 'Products retrieved successfully',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param ProductRequest $request
     * @return JsonResponse
     */
    public function store(ProductRequest $request): JsonResponse
    {
        $product = $this->productService->store($request->validated());

        return response()->json([
            'success' => true,
            'data' => new ProductResource($product),
            'message' => 'Product created successfully',
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param Product $product
     * @return JsonResponse
     */
    public function show(Product $product): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new ProductResource($product),
            'message' => 'Product retrieved successfully',
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param ProductRequest $request
     * @param Product $product
     * @return JsonResponse
     */
    public function update(ProductRequest $request, Product $product): JsonResponse
    {
        $product = $this->productService->update($request->validated(), $product);

        return response()->json([
            'success' => true,
            'data' => new ProductResource($product),
            'message' => 'Product updated successfully',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Product $product
     * @return JsonResponse
     */
    public function destroy(Product $product): JsonResponse
    {
        $this->productService->destroy($product->id);

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully',
        ]);
    }

    /**
     * Search for products.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        $products = $this->productService->product_search($request->all());

        return response()->json([
            'success' => true,
            'data' => ProductResource::collection($products),
            'message' => 'Products retrieved successfully',
        ]);
    }
}