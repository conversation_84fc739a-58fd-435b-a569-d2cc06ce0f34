<?php

declare(strict_types=1);

namespace Modules\Product\app\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\Product\app\Models\Product;
use Modules\Product\app\Services\ProductService;
use Modules\Product\app\Services\ProductTaxService;
use Modules\Product\app\Services\ProductFlashDealService;
use Modules\Product\app\Services\ProductStockService;
use Modules\Product\app\Services\FrequentlyBoughtProductService;
use Modules\Product\app\Http\Requests\ProductRequest;

class ProductController extends Controller
{
    /**
     * The product service instance.
     *
     * @var ProductService
     */
    protected ProductService $productService;

    /**
     * The product tax service instance.
     *
     * @var ProductTaxService
     */
    protected ProductTaxService $productTaxService;

    /**
     * The product flash deal service instance.
     *
     * @var ProductFlashDealService
     */
    protected ProductFlashDealService $productFlashDealService;

    /**
     * The product stock service instance.
     *
     * @var ProductStockService
     */
    protected ProductStockService $productStockService;

    /**
     * The frequently bought product service instance.
     *
     * @var FrequentlyBoughtProductService
     */
    protected FrequentlyBoughtProductService $frequentlyBoughtProductService;

    /**
     * Create a new controller instance.
     *
     * @param ProductService $productService
     * @param ProductTaxService $productTaxService
     * @param ProductFlashDealService $productFlashDealService
     * @param ProductStockService $productStockService
     * @param FrequentlyBoughtProductService $frequentlyBoughtProductService
     */
    public function __construct(
        ProductService $productService,
        ProductTaxService $productTaxService,
        ProductFlashDealService $productFlashDealService,
        ProductStockService $productStockService,
        FrequentlyBoughtProductService $frequentlyBoughtProductService
    ) {
        $this->productService = $productService;
        $this->productTaxService = $productTaxService;
        $this->productFlashDealService = $productFlashDealService;
        $this->productStockService = $productStockService;
        $this->frequentlyBoughtProductService = $frequentlyBoughtProductService;

        // Staff Permission Check
        $this->middleware(['permission:add_new_product'])->only('create');
        $this->middleware(['permission:show_all_products'])->only('index');
        $this->middleware(['permission:product_edit'])->only('edit');
        $this->middleware(['permission:product_duplicate'])->only('duplicate');
        $this->middleware(['permission:product_delete'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return View
     */
    public function index(Request $request): View
    {
        $products = Product::query()
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', '%' . $search . '%')
                    ->orWhereHas('stocks', function ($q) use ($search) {
                        $q->where('sku', 'like', '%' . $search . '%');
                    });
            })
            ->when($request->type, function ($query, $type) {
                $parts = explode(',', $type);
                if (count($parts) === 2) {
                    return $query->orderBy($parts[0], $parts[1]);
                }
                return $query;
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('product::products.index', compact('products'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create(): View
    {
        return view('product::products.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param ProductRequest $request
     * @return RedirectResponse
     */
    public function store(ProductRequest $request): RedirectResponse
    {
        $product = $this->productService->store($request->except([
            '_token', 'sku', 'choice', 'tax_id', 'tax', 'tax_type', 'flash_deal_id', 'flash_discount', 'flash_discount_type'
        ]));
        
        $request->merge(['product_id' => $product->id]);

        // Product categories
        $product->categories()->attach($request->category_ids);

        // VAT & Tax
        if ($request->tax_id) {
            $this->productTaxService->store($request->only([
                'tax_id', 'tax', 'tax_type', 'product_id'
            ]));
        }

        // Flash Deal
        $this->productFlashDealService->store($request->only([
            'flash_deal_id', 'flash_discount', 'flash_discount_type'
        ]), $product);

        // Product Stock
        $this->productStockService->store($request->only([
            'colors_active', 'colors', 'choice_no', 'unit_price', 'sku', 'current_stock', 'product_id'
        ]), $product);

        // Frequently Bought Products
        $this->frequentlyBoughtProductService->store($request->only([
            'product_id', 'frequently_bought_selection_type', 'fq_bought_product_ids', 'fq_bought_product_category_id'
        ]));

        flash(translate('Product has been inserted successfully'))->success();

        return redirect()->route('products.index');
    }

    /**
     * Display the specified resource.
     *
     * @param Product $product
     * @return View
     */
    public function show(Product $product): View
    {
        return view('product::products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param Product $product
     * @return View
     */
    public function edit(Product $product): View
    {
        return view('product::products.edit', compact('product'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param ProductRequest $request
     * @param Product $product
     * @return RedirectResponse
     */
    public function update(ProductRequest $request, Product $product): RedirectResponse
    {
        $product = $this->productService->update($request->except([
            '_token', 'sku', 'choice', 'tax_id', 'tax', 'tax_type', 'flash_deal_id', 'flash_discount', 'flash_discount_type'
        ]), $product);

        $request->merge(['product_id' => $product->id]);

        // Product categories
        $product->categories()->sync($request->category_ids);

        // Product Stock
        $product->stocks()->delete();
        $this->productStockService->store($request->only([
            'colors_active', 'colors', 'choice_no', 'unit_price', 'sku', 'current_stock', 'product_id'
        ]), $product);

        // Flash Deal
        $this->productFlashDealService->store($request->only([
            'flash_deal_id', 'flash_discount', 'flash_discount_type'
        ]), $product);

        // VAT & Tax
        if ($request->tax_id) {
            $product->taxes()->delete();
            $this->productTaxService->store($request->only([
                'tax_id', 'tax', 'tax_type', 'product_id'
            ]));
        }

        // Frequently Bought Products
        $product->frequently_bought_products()->delete();
        $this->frequentlyBoughtProductService->store($request->only([
            'product_id', 'frequently_bought_selection_type', 'fq_bought_product_ids', 'fq_bought_product_category_id'
        ]));

        flash(translate('Product has been updated successfully'))->success();

        return redirect()->route('products.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Product $product
     * @return RedirectResponse
     */
    public function destroy(Product $product): RedirectResponse
    {
        $this->productService->destroy($product->id);

        flash(translate('Product has been deleted successfully'))->success();

        return back();
    }

    /**
     * Duplicate the specified resource.
     *
     * @param Product $product
     * @return RedirectResponse
     */
    public function duplicate(Product $product): RedirectResponse
    {
        $product_new = $this->productService->product_duplicate_store($product);

        // Product Stock
        $this->productStockService->product_duplicate_store($product->stocks, $product_new);

        // VAT & Tax
        $this->productTaxService->product_duplicate_store($product->taxes, $product_new);
        
        // Product Categories
        foreach ($product->product_categories as $product_category) {
            $product_new->categories()->attach($product_category->category_id);
        }

        // Frequently Bought Products
        $this->frequentlyBoughtProductService->product_duplicate_store($product->frequently_bought_products, $product_new);

        flash(translate('Product has been duplicated successfully'))->success();
        
        return redirect()->route('products.index');
    }
}