<?php

declare(strict_types=1);

namespace Modules\Product\app\Http\Requests\Api;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class ProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|max:255',
            'category_ids' => 'required|array',
            'category_ids.*' => 'required|exists:categories,id',
            'brand_id' => 'nullable|exists:brands,id',
            'unit' => 'required|max:20',
            'min_qty' => 'required|numeric|min:1',
            'tags' => 'required',
            'description' => 'required',
            'unit_price' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'discount_type' => 'nullable|in:amount,percent',
            'date_range' => 'nullable',
            'sku' => 'nullable|max:255',
            'current_stock' => 'nullable|numeric',
            'thumbnail_img' => 'required',
            'photos' => 'required',
            'shipping_type' => 'nullable|in:free,flat_rate',
            'flat_shipping_cost' => 'nullable|numeric|required_if:shipping_type,flat_rate',
            'meta_title' => 'nullable|max:255',
            'meta_description' => 'nullable',
            'meta_img' => 'nullable',
            'pdf' => 'nullable|mimes:pdf',
            'colors' => 'nullable|array',
            'colors.*' => 'nullable|exists:colors,code',
            'choice_no' => 'nullable|array',
            'choice_options' => 'nullable|array',
            'tax_id' => 'nullable|exists:taxes,id',
            'tax' => 'nullable|numeric|min:0',
            'tax_type' => 'nullable|in:amount,percent',
            'flash_deal_id' => 'nullable|exists:flash_deals,id',
            'flash_discount' => 'nullable|numeric|min:0',
            'flash_discount_type' => 'nullable|in:amount,percent',
            'frequently_bought_selection_type' => 'nullable|in:manual,category',
            'fq_bought_product_ids' => 'nullable|array|required_if:frequently_bought_selection_type,manual',
            'fq_bought_product_ids.*' => 'nullable|exists:products,id',
            'fq_bought_product_category_id' => 'nullable|exists:categories,id|required_if:frequently_bought_selection_type,category',
        ];

        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['name'] = 'sometimes|required|max:255';
            $rules['category_ids'] = 'sometimes|required|array';
            $rules['unit'] = 'sometimes|required|max:20';
            $rules['min_qty'] = 'sometimes|required|numeric|min:1';
            $rules['tags'] = 'sometimes|required';
            $rules['description'] = 'sometimes|required';
            $rules['unit_price'] = 'sometimes|required|numeric|min:0';
            $rules['thumbnail_img'] = 'sometimes|required';
            $rules['photos'] = 'sometimes|required';
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Product name is required',
            'category_ids.required' => 'At least one category is required',
            'category_ids.*.exists' => 'Selected category does not exist',
            'unit.required' => 'Unit is required',
            'min_qty.required' => 'Minimum purchase quantity is required',
            'min_qty.min' => 'Minimum purchase quantity must be at least 1',
            'tags.required' => 'Tags are required',
            'description.required' => 'Description is required',
            'unit_price.required' => 'Unit price is required',
            'unit_price.min' => 'Unit price must be greater than or equal to 0',
            'thumbnail_img.required' => 'Thumbnail image is required',
            'photos.required' => 'Product images are required',
            'flat_shipping_cost.required_if' => 'Flat shipping cost is required when shipping type is flat rate',
            'fq_bought_product_ids.required_if' => 'Product IDs are required when selection type is manual',
            'fq_bought_product_category_id.required_if' => 'Category ID is required when selection type is category',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param Validator $validator
     * @return void
     *
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => 'Validation errors',
            'errors' => $validator->errors(),
        ], 422));
    }
}