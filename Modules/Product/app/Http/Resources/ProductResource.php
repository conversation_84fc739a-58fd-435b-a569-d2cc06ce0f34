<?php

declare(strict_types=1);

namespace Modules\Product\app\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'added_by' => $this->added_by,
            'user_id' => $this->user_id,
            'category_id' => $this->category_id,
            'brand_id' => $this->brand_id,
            'photos' => $this->photos,
            'thumbnail_img' => $this->thumbnail_img,
            'video_provider' => $this->video_provider,
            'video_link' => $this->video_link,
            'tags' => $this->tags,
            'description' => $this->description,
            'unit_price' => (float) $this->unit_price,
            'purchase_price' => (float) $this->purchase_price,
            'variant_product' => (bool) $this->variant_product,
            'attributes' => $this->attributes,
            'choice_options' => $this->choice_options,
            'colors' => $this->colors,
            'variations' => $this->variations,
            'todays_deal' => (bool) $this->todays_deal,
            'published' => (bool) $this->published,
            'approved' => (bool) $this->approved,
            'stock_visibility_state' => $this->stock_visibility_state,
            'cash_on_delivery' => (bool) $this->cash_on_delivery,
            'featured' => (bool) $this->featured,
            'seller_featured' => (bool) $this->seller_featured,
            'current_stock' => (int) $this->current_stock,
            'unit' => $this->unit,
            'min_qty' => (int) $this->min_qty,
            'low_stock_quantity' => (int) $this->low_stock_quantity,
            'discount' => (float) $this->discount,
            'discount_type' => $this->discount_type,
            'discount_start_date' => $this->discount_start_date,
            'discount_end_date' => $this->discount_end_date,
            'shipping_type' => $this->shipping_type,
            'shipping_cost' => (float) $this->shipping_cost,
            'is_quantity_multiplied' => (bool) $this->is_quantity_multiplied,
            'est_shipping_days' => $this->est_shipping_days,
            'num_of_sale' => (int) $this->num_of_sale,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_img' => $this->meta_img,
            'pdf' => $this->pdf,
            'rating' => (float) $this->rating,
            'barcode' => $this->barcode,
            'digital' => (bool) $this->digital,
            'auction_product' => (bool) $this->auction_product,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'external_link' => $this->external_link,
            'external_link_btn' => $this->external_link_btn,
            'wholesale_product' => (bool) $this->wholesale_product,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'categories' => $this->whenLoaded('categories', function () {
                return $this->categories->map(function ($category) {
                    return [
                        'id' => $category->id,
                        'name' => $category->name,
                        'slug' => $category->slug,
                    ];
                });
            }),
            'brand' => $this->whenLoaded('brand', function () {
                return [
                    'id' => $this->brand->id,
                    'name' => $this->brand->name,
                    'slug' => $this->brand->slug,
                ];
            }),
            'user' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'name' => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),
            'stocks' => $this->whenLoaded('stocks', function () {
                return $this->stocks->map(function ($stock) {
                    return [
                        'id' => $stock->id,
                        'product_id' => $stock->product_id,
                        'variant' => $stock->variant,
                        'sku' => $stock->sku,
                        'price' => (float) $stock->price,
                        'qty' => (int) $stock->qty,
                        'image' => $stock->image,
                    ];
                });
            }),
            'taxes' => $this->whenLoaded('taxes', function () {
                return $this->taxes->map(function ($tax) {
                    return [
                        'id' => $tax->id,
                        'product_id' => $tax->product_id,
                        'tax_id' => $tax->tax_id,
                        'tax' => (float) $tax->tax,
                        'tax_type' => $tax->tax_type,
                    ];
                });
            }),
        ];
    }
}
