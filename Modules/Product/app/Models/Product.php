<?php

declare(strict_types=1);

namespace Modules\Product\app\Models;

use App;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Product\database\factories\ProductFactory;
use App\Traits\PreventDemoModeChanges;

class Product extends Model
{
    use PreventDemoModeChanges;
    /** @use HasFactory<ProductFactory> */
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'slug',
        'added_by',
        'user_id',
        'category_id',
        'brand_id',
        'photos',
        'thumbnail_img',
        'video_provider',
        'video_link',
        'tags',
        'description',
        'unit_price',
        'purchase_price',
        'variant_product',
        'attributes',
        'choice_options',
        'colors',
        'variations',
        'todays_deal',
        'published',
        'approved',
        'stock_visibility_state',
        'cash_on_delivery',
        'featured',
        'seller_featured',
        'current_stock',
        'unit',
        'min_qty',
        'low_stock_quantity',
        'discount',
        'discount_type',
        'discount_start_date',
        'discount_end_date',
        'shipping_type',
        'shipping_cost',
        'is_quantity_multiplied',
        'est_shipping_days',
        'num_of_sale',
        'meta_title',
        'meta_description',
        'meta_img',
        'pdf',
        'slug',
        'rating',
        'barcode',
        'digital',
        'auction_product',
        'file_name',
        'file_path',
        'external_link',
        'external_link_btn',
        'wholesale_product',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'discount_start_date' => 'timestamp',
        'discount_end_date' => 'timestamp',
        'published' => 'boolean',
        'approved' => 'boolean',
        'featured' => 'boolean',
        'todays_deal' => 'boolean',
        'digital' => 'boolean',
        'auction_product' => 'boolean',
        'wholesale_product' => 'boolean',
    ];

    /**
     * The relationships that should always be loaded.
     *
     * @var array<int, string>
     */
    protected $with = ['product_translations', 'taxes', 'thumbnail'];

    /**
     * Get the translation for the specified field.
     *
     * @param string $field The field to get the translation for
     * @param string|bool $lang The language to get the translation for, or false to use the current locale
     * @return mixed The translated value
     */
    public function getTranslation(string $field = '', bool|string $lang = false): mixed
    {
        $lang = $lang === false ? App::getLocale() : $lang;
        $product_translations = $this->product_translations->where('lang', $lang)->first();
        return $product_translations != null ? $product_translations->$field : $this->$field;
    }

    /**
     * Get the product translations for this product.
     *
     * @return HasMany<ProductTranslation>
     */
    public function product_translations(): HasMany
    {
        return $this->hasMany(ProductTranslation::class);
    }

    /**
     * Get the main category for this product.
     *
     * @return BelongsTo<Category, Product>
     */
    public function main_category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }
    
    /**
     * Get the categories for this product.
     *
     * @return BelongsToMany<Category>
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'product_categories');
    }

    /**
     * Get the frequently bought products for this product.
     *
     * @return HasMany<FrequentlyBoughtProduct>
     */
    public function frequently_bought_products(): HasMany
    {
        return $this->hasMany(FrequentlyBoughtProduct::class);
    }

    /**
     * Get the product categories for this product.
     *
     * @return HasMany<ProductCategory>
     */
    public function product_categories(): HasMany
    {
        return $this->hasMany(ProductCategory::class);
    }

    /**
     * Get the brand for this product.
     *
     * @return BelongsTo<Brand, Product>
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the user for this product.
     *
     * @return BelongsTo<User, Product>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order details for this product.
     *
     * @return HasMany<OrderDetail>
     */
    public function orderDetails(): HasMany
    {
        return $this->hasMany(OrderDetail::class);
    }

    /**
     * Get the reviews for this product.
     *
     * @return HasMany<Review>
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the product queries for this product.
     *
     * @return HasMany<ProductQuery>
     */
    public function product_queries(): HasMany
    {
        return $this->hasMany(ProductQuery::class);
    }

    /**
     * Get the wishlists for this product.
     *
     * @return HasMany<Wishlist>
     */
    public function wishlists(): HasMany
    {
        return $this->hasMany(Wishlist::class);
    }

    /**
     * Get the stocks for this product.
     *
     * @return HasMany<ProductStock>
     */
    public function stocks(): HasMany
    {
        return $this->hasMany(ProductStock::class);
    }

    /**
     * Get the taxes for this product.
     *
     * @return HasMany<ProductTax>
     */
    public function taxes(): HasMany
    {
        return $this->hasMany(ProductTax::class);
    }

    /**
     * Get the flash deal products for this product.
     *
     * @return HasMany<FlashDealProduct>
     */
    public function flash_deal_products(): HasMany
    {
        return $this->hasMany(FlashDealProduct::class);
    }

    /**
     * Get the bids for this product.
     *
     * @return HasMany<AuctionProductBid>
     */
    public function bids(): HasMany
    {
        return $this->hasMany(AuctionProductBid::class);
    }

    /**
     * Get the thumbnail for this product.
     *
     * @return BelongsTo<Upload, Product>
     */
    public function thumbnail(): BelongsTo
    {
        return $this->belongsTo(Upload::class, 'thumbnail_img');
    }

    /**
     * Scope a query to only include physical products.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Product> $query
     * @return \Illuminate\Database\Eloquent\Builder<Product>
     */
    public function scopePhysical($query)
    {
        return $query->where('digital', 0);
    }

    /**
     * Scope a query to only include digital products.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Product> $query
     * @return \Illuminate\Database\Eloquent\Builder<Product>
     */
    public function scopeDigital($query)
    {
        return $query->where('digital', 1);
    }

    /**
     * Get the carts for this product.
     *
     * @return HasMany<Cart>
     */
    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class);
    }
    
    /**
     * Scope a query to only include approved and published products.
     *
     * @param \Illuminate\Database\Eloquent\Builder<Product> $query
     * @return \Illuminate\Database\Eloquent\Builder<Product>
     */
    public function scopeIsApprovedPublished($query)
    {
        return $query->where('approved', '1')->where('published', 1);
    }

    /**
     * Get the last viewed products for this product.
     *
     * @return HasMany<LastViewedProduct>
     */
    public function last_viewed_products(): HasMany
    {
        return $this->hasMany(LastViewedProduct::class);
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory<Product>
     */
    protected static function newFactory()
    {
        return ProductFactory::new();
    }
}