<?php

declare(strict_types=1);

namespace Modules\Product\app\Services;

use AizPackages\CombinationGenerate\Services\CombinationService;
use App\Models\User;
use App\Utility\ProductUtility;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Modules\Product\app\Models\Product;

class ProductService
{
    /**
     * Store a new product.
     *
     * @param array<string, mixed> $data The product data
     * @return Product The created product
     */
    public function store(array $data): Product
    {
        $collection = collect($data);

        $approved = 1;
        if (auth()->user()->user_type == 'seller') {
            $user_id = auth()->user()->id;
            if (get_setting('product_approve_by_admin') == 1) {
                $approved = 0;
            }
        } else {
            $user_id = User::where('user_type', 'admin')->first()->id;
        }

        $tags = $this->processTags($collection);
        $collection['tags'] = implode(',', $tags);

        [$discount_start_date, $discount_end_date] = $this->processDiscountDates($collection);
        unset($collection['date_range']);

        $this->processMetaData($collection);

        $shipping_cost = $this->processShippingCost($collection);
        unset($collection['flat_shipping_cost']);

        $slug = $this->generateSlug($collection['name']);

        $colors = $this->processColors($collection);

        $options = ProductUtility::get_attribute_options($collection);
        $this->processCombinations($options, $collection);

        unset($collection['colors_active']);

        $choice_options = $this->processChoiceOptions($collection);

        $attributes = $this->processAttributes($collection);

        $published = $this->processPublishedStatus($collection);
        unset($collection['button']);

        $data = $collection->merge(compact(
            'user_id',
            'approved',
            'discount_start_date',
            'discount_end_date',
            'shipping_cost',
            'slug',
            'colors',
            'choice_options',
            'attributes',
            'published',
        ))->toArray();

        return Product::create($data);
    }

    /**
     * Update an existing product.
     *
     * @param array<string, mixed> $data The product data
     * @param Product $product The product to update
     * @return Product The updated product
     */
    public function update(array $data, Product $product): Product
    {
        $collection = collect($data);

        $slug = $this->updateSlug($collection);

        $this->processCheckboxFields($collection);

        $tags = $this->processTags($collection);
        $collection['tags'] = implode(',', $tags);

        [$discount_start_date, $discount_end_date] = $this->processDiscountDates($collection);
        unset($collection['date_range']);

        $this->processMetaData($collection);

        $this->processLanguageFields($collection);

        $shipping_cost = $this->processShippingCost($collection);
        unset($collection['flat_shipping_cost']);

        $colors = $this->processColors($collection);

        $options = ProductUtility::get_attribute_options($collection);
        $this->processCombinations($options, $collection);

        unset($collection['colors_active']);

        $choice_options = $this->processChoiceOptions($collection);

        $attributes = $this->processAttributes($collection);

        unset($collection['button']);

        $data = $collection->merge(compact(
            'discount_start_date',
            'discount_end_date',
            'shipping_cost',
            'slug',
            'colors',
            'choice_options',
            'attributes',
        ))->toArray();

        $product->update($data);

        return $product;
    }

    /**
     * Duplicate a product.
     *
     * @param Product $product The product to duplicate
     * @return Product The duplicated product
     */
    public function product_duplicate_store(Product $product): Product
    {
        $product_new = $product->replicate();
        $product_new->slug = $product_new->slug . '-' . Str::random(5);
        $product_new->approved = (get_setting('product_approve_by_admin') == 1 && $product->added_by != 'admin') ? 0 : 1;
        $product_new->save();

        return $product_new;
    }

    /**
     * Delete a product.
     *
     * @param int $id The ID of the product to delete
     * @return void
     */
    public function destroy(int $id): void
    {
        $product = Product::findOrFail($id);
        $product->product_translations()->delete();
        $product->categories()->detach();
        $product->stocks()->delete();
        $product->taxes()->delete();
        $product->wishlists()->delete();
        $product->carts()->delete();
        $product->frequently_bought_products()->delete();
        $product->last_viewed_products()->delete();
        $product->flash_deal_products()->delete();
        deleteProductReview($product);
        Product::destroy($id);
    }

    /**
     * Search for products.
     *
     * @param array<string, mixed> $data The search parameters
     * @return Collection<int, Product> The search results
     */
    public function product_search(array $data): Collection
    {
        $collection = collect($data);
        $auth_user = auth()->user();
        $productType = $collection['product_type'];
        $products = Product::query();

        if ($collection['category'] != null) {
            $category = Category::with('childrenCategories')->find($collection['category']);
            $products = $category->products();
        }

        $products = in_array($auth_user->user_type, ['admin', 'staff'])
            ? $products->where('products.added_by', 'admin')
            : $products->where('products.user_id', $auth_user->id);

        $products->where('published', '1')->where('auction_product', 0)->where('approved', '1');

        if ($productType == 'physical') {
            $products->where('digital', 0)->where('wholesale_product', 0);
        } elseif ($productType == 'digital') {
            $products->where('digital', 1);
        } elseif ($productType == 'wholesale') {
            $products->where('wholesale_product', 1);
        }

        if ($collection['product_id'] != null) {
            $products->where('id', '!=', $collection['product_id']);
        }

        if ($collection['search_key'] != null) {
            $products->where('name', 'like', '%' . $collection['search_key'] . '%');
        }

        return $products->limit(20)->get();
    }

    /**
     * Set category-wise discount for products.
     *
     * @param array<string, mixed> $data The discount data
     * @return int 1 on success
     */
    public function setCategoryWiseDiscount(array $data): int
    {
        $auth_user = auth()->user();
        $discount_start_date = null;
        $discount_end_date = null;

        if ($data['date_range'] != null) {
            $date_var = explode(" to ", $data['date_range']);
            $discount_start_date = strtotime($date_var[0]);
            $discount_end_date = strtotime($date_var[1]);
        }

        $seller_product_discount = $data['seller_product_discount'] ?? null;
        $admin_id = User::where('user_type', 'admin')->first()->id;

        $products = Product::where('category_id', $data['category_id'])->where('auction_product', 0);

        if (in_array($auth_user->user_type, ['admin', 'staff']) && $seller_product_discount == 0) {
            $products = $products->where('user_id', $admin_id);
        } elseif ($auth_user->user_type == 'seller') {
            $products = $products->where('user_id', $auth_user->id);
        }

        $products->update([
            'discount' => $data['discount'],
            'discount_type' => 'percent',
            'discount_start_date' => $discount_start_date,
            'discount_end_date' => $discount_end_date,
        ]);

        return 1;
    }

    /**
     * Process tags from the collection.
     *
     * @param Collection $collection The collection containing the tags
     * @return array<int, string> The processed tags
     */
    private function processTags(Collection $collection): array
    {
        $tags = [];
        if ($collection['tags'][0] != null) {
            foreach (json_decode($collection['tags'][0]) as $tag) {
                $tags[] = $tag->value;
            }
        }
        return $tags;
    }

    /**
     * Process discount dates from the collection.
     *
     * @param Collection $collection The collection containing the date range
     * @return array{0: ?int, 1: ?int} The discount start and end dates
     */
    private function processDiscountDates(Collection $collection): array
    {
        $discount_start_date = null;
        $discount_end_date = null;

        if ($collection['date_range'] != null) {
            $date_var = explode(" to ", $collection['date_range']);
            $discount_start_date = strtotime($date_var[0]);
            $discount_end_date = strtotime($date_var[1]);
        }

        return [$discount_start_date, $discount_end_date];
    }

    /**
     * Process meta data from the collection.
     *
     * @param Collection $collection The collection containing the meta data
     * @return void
     */
    private function processMetaData(Collection $collection): void
    {
        if ($collection['meta_title'] == null) {
            $collection['meta_title'] = $collection['name'];
        }

        if ($collection['meta_description'] == null) {
            $collection['meta_description'] = strip_tags($collection['description']);
        }

        if ($collection['meta_img'] == null) {
            $collection['meta_img'] = $collection['thumbnail_img'];
        }
    }

    /**
     * Process shipping cost from the collection.
     *
     * @param Collection $collection The collection containing the shipping data
     * @return int The shipping cost
     */
    private function processShippingCost(Collection $collection): int
    {
        $shipping_cost = 0;

        if (isset($collection['shipping_type'])) {
            if ($collection['shipping_type'] == 'free') {
                $shipping_cost = 0;
            } elseif ($collection['shipping_type'] == 'flat_rate') {
                $shipping_cost = $collection['flat_shipping_cost'];
            }
        }

        return $shipping_cost;
    }

    /**
     * Generate a slug for the product.
     *
     * @param string $name The product name
     * @return string The generated slug
     */
    private function generateSlug(string $name): string
    {
        $slug = Str::slug($name);
        $same_slug_count = Product::where('slug', 'LIKE', $slug . '%')->count();
        $slug_suffix = $same_slug_count ? '-' . ($same_slug_count + 1) : '';

        return $slug . $slug_suffix;
    }

    /**
     * Update the slug for the product.
     *
     * @param Collection $collection The collection containing the slug data
     * @return string The updated slug
     */
    private function updateSlug(Collection $collection): string
    {
        $slug = Str::slug($collection['name']);
        $slug = $collection['slug'] ? Str::slug($collection['slug']) : Str::slug($collection['name']);
        $same_slug_count = Product::where('slug', 'LIKE', $slug . '%')->count();
        $slug_suffix = $same_slug_count > 1 ? '-' . ($same_slug_count + 1) : '';

        return $slug . $slug_suffix;
    }

    /**
     * Process checkbox fields in the collection.
     *
     * @param Collection $collection The collection containing the checkbox fields
     * @return void
     */
    private function processCheckboxFields(Collection $collection): void
    {
        if (addon_is_activated('refund_request') && !isset($collection['refundable'])) {
            $collection['refundable'] = 0;
        }

        if (!isset($collection['is_quantity_multiplied'])) {
            $collection['is_quantity_multiplied'] = 0;
        }

        if (!isset($collection['cash_on_delivery'])) {
            $collection['cash_on_delivery'] = 0;
        }

        if (!isset($collection['featured'])) {
            $collection['featured'] = 0;
        }

        if (!isset($collection['todays_deal'])) {
            $collection['todays_deal'] = 0;
        }
    }

    /**
     * Process language fields in the collection.
     *
     * @param Collection $collection The collection containing the language fields
     * @return void
     */
    private function processLanguageFields(Collection $collection): void
    {
        if ($collection['lang'] != env("DEFAULT_LANGUAGE")) {
            unset($collection['name']);
            unset($collection['unit']);
            unset($collection['description']);
        }

        unset($collection['lang']);
    }

    /**
     * Process colors from the collection.
     *
     * @param Collection $collection The collection containing the colors
     * @return string The JSON encoded colors
     */
    private function processColors(Collection $collection): string
    {
        $colors = json_encode([]);

        if (
            isset($collection['colors_active']) &&
            $collection['colors_active'] &&
            $collection['colors'] &&
            count($collection['colors']) > 0
        ) {
            $colors = json_encode($collection['colors']);
        }

        return $colors;
    }

    /**
     * Process combinations from the options.
     *
     * @param array<int, mixed> $options The options to generate combinations from
     * @param Collection $collection The collection to update
     * @return void
     */
    private function processCombinations(array $options, Collection $collection): void
    {
        $combinations = (new CombinationService())->generate_combination($options);

        if (count($combinations) > 0) {
            foreach ($combinations as $combination) {
                $str = ProductUtility::get_combination_string($combination, $collection);

                unset($collection['price_' . str_replace('.', '_', $str)]);
                unset($collection['sku_' . str_replace('.', '_', $str)]);
                unset($collection['qty_' . str_replace('.', '_', $str)]);
                unset($collection['img_' . str_replace('.', '_', $str)]);
            }
        }
    }

    /**
     * Process choice options from the collection.
     *
     * @param Collection $collection The collection containing the choice options
     * @return string The JSON encoded choice options
     */
    private function processChoiceOptions(Collection $collection): string
    {
        $choice_options = [];

        if (isset($collection['choice_no']) && $collection['choice_no']) {
            $str = '';
            $item = [];

            foreach ($collection['choice_no'] as $no) {
                $str = 'choice_options_' . $no;
                $item['attribute_id'] = $no;
                $attribute_data = [];

                foreach ($collection[$str] as $eachValue) {
                    $attribute_data[] = $eachValue;
                }

                unset($collection[$str]);

                $item['values'] = $attribute_data;
                $choice_options[] = $item;
            }
        }

        return json_encode($choice_options, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Process attributes from the collection.
     *
     * @param Collection $collection The collection containing the attributes
     * @return string The JSON encoded attributes
     */
    private function processAttributes(Collection $collection): string
    {
        if (isset($collection['choice_no']) && $collection['choice_no']) {
            $attributes = json_encode($collection['choice_no']);
            unset($collection['choice_no']);
            return $attributes;
        }

        return json_encode([]);
    }

    /**
     * Process the published status from the collection.
     *
     * @param Collection $collection The collection containing the button value
     * @return int The published status (0 or 1)
     */
    private function processPublishedStatus(Collection $collection): int
    {
        $published = 1;

        if ($collection['button'] == 'unpublish' || $collection['button'] == 'draft') {
            $published = 0;
        }

        return $published;
    }
}