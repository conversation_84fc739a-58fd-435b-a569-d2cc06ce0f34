<?php

declare(strict_types=1);

namespace Modules\Product\database\factories;

use App\Models\Brand;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\Product\app\Models\Product;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\Product\app\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        $slug = Str::slug($name);

        return [
            'name' => $name,
            'slug' => $slug,
            'added_by' => 'admin',
            'user_id' => User::where('user_type', 'admin')->first()->id ?? 1,
            'category_id' => Category::inRandomOrder()->first()->id ?? 1,
            'brand_id' => Brand::inRandomOrder()->first()->id ?? null,
            'photos' => json_encode([$this->faker->imageUrl()]),
            'thumbnail_img' => $this->faker->imageUrl(),
            'video_provider' => null,
            'video_link' => null,
            'tags' => implode(',', $this->faker->words(5)),
            'description' => $this->faker->paragraph(),
            'unit_price' => $this->faker->randomFloat(2, 10, 1000),
            'purchase_price' => $this->faker->randomFloat(2, 5, 800),
            'variant_product' => 0,
            'attributes' => json_encode([]),
            'choice_options' => json_encode([]),
            'colors' => json_encode([]),
            'variations' => json_encode([]),
            'todays_deal' => $this->faker->boolean(20),
            'published' => 1,
            'approved' => 1,
            'stock_visibility_state' => 'quantity',
            'cash_on_delivery' => 1,
            'featured' => $this->faker->boolean(10),
            'seller_featured' => 0,
            'current_stock' => $this->faker->numberBetween(0, 100),
            'unit' => $this->faker->randomElement(['pcs', 'kg', 'gm', 'ltr']),
            'min_qty' => 1,
            'low_stock_quantity' => 5,
            'discount' => $this->faker->numberBetween(0, 50),
            'discount_type' => 'percent',
            'discount_start_date' => null,
            'discount_end_date' => null,
            'shipping_type' => 'free',
            'shipping_cost' => 0,
            'is_quantity_multiplied' => 0,
            'est_shipping_days' => null,
            'num_of_sale' => $this->faker->numberBetween(0, 100),
            'meta_title' => $name,
            'meta_description' => $this->faker->sentence(),
            'meta_img' => $this->faker->imageUrl(),
            'pdf' => null,
            'rating' => $this->faker->randomFloat(1, 0, 5),
            'barcode' => $this->faker->ean13(),
            'digital' => 0,
            'auction_product' => 0,
            'file_name' => null,
            'file_path' => null,
            'external_link' => null,
            'external_link_btn' => null,
            'wholesale_product' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the product is published.
     *
     * @return Factory<Product>
     */
    public function published(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'published' => 1,
            ];
        });
    }

    /**
     * Indicate that the product is featured.
     *
     * @return Factory<Product>
     */
    public function featured(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'featured' => 1,
            ];
        });
    }

    /**
     * Indicate that the product is digital.
     *
     * @return Factory<Product>
     */
    public function digital(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'digital' => 1,
                'file_name' => $this->faker->word() . '.pdf',
                'file_path' => 'uploads/products/digital/' . $this->faker->uuid() . '.pdf',
            ];
        });
    }
}