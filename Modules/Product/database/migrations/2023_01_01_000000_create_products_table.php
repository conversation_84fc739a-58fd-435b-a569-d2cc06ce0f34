<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('added_by')->default('admin');
            $table->foreignUuid('user_id')->constrained();
            $table->foreignUuid('category_id')->nullable()->constrained();
            $table->foreignUuid('brand_id')->nullable()->constrained();
            $table->json('photos');
            $table->string('thumbnail_img')->nullable();
            $table->string('video_provider')->nullable();
            $table->string('video_link')->nullable();
            $table->text('tags')->nullable();
            $table->longText('description')->nullable();
            $table->decimal('unit_price', 10, 2)->default(0.00);
            $table->decimal('purchase_price', 10, 2)->default(0.00);
            $table->boolean('variant_product')->default(false);
            $table->json('attributes')->nullable();
            $table->json('choice_options')->nullable();
            $table->json('colors')->nullable();
            $table->json('variations')->nullable();
            $table->boolean('todays_deal')->default(false);
            $table->boolean('published')->default(true);
            $table->boolean('approved')->default(true);
            $table->string('stock_visibility_state')->default('quantity');
            $table->boolean('cash_on_delivery')->default(true);
            $table->boolean('featured')->default(false);
            $table->boolean('seller_featured')->default(false);
            $table->integer('current_stock')->default(0);
            $table->string('unit')->nullable();
            $table->integer('min_qty')->default(1);
            $table->integer('low_stock_quantity')->nullable();
            $table->decimal('discount', 10, 2)->nullable();
            $table->string('discount_type')->nullable();
            $table->integer('discount_start_date')->nullable();
            $table->integer('discount_end_date')->nullable();
            $table->string('shipping_type')->nullable();
            $table->decimal('shipping_cost', 10, 2)->nullable();
            $table->boolean('is_quantity_multiplied')->default(false);
            $table->string('est_shipping_days')->nullable();
            $table->integer('num_of_sale')->default(0);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_img')->nullable();
            $table->string('pdf')->nullable();
            $table->decimal('rating', 3, 2)->default(0.00);
            $table->string('barcode')->nullable();
            $table->boolean('digital')->default(false);
            $table->boolean('auction_product')->default(false);
            $table->string('file_name')->nullable();
            $table->string('file_path')->nullable();
            $table->string('external_link')->nullable();
            $table->string('external_link_btn')->nullable();
            $table->boolean('wholesale_product')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};