<?php

declare(strict_types=1);

namespace Modules\Product\tests\Feature;

use Modules\Product\app\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ProductTest extends TestCase
{
    /**
     * Test that a user can view the products index page.
     */
    public function test_user_can_view_products_index(): void
    {
        $this
            ->getJson(route('api.v1.products.index'))
            ->assertOk()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data',
                    'pagination',
                ],
                'message',
            ]);
    }

    /**
     * Test that a user can view a single product.
     */
    public function test_user_can_view_single_product(): void
    {
        $product = Product::factory()->create();

        $this
            ->getJson(route('api.v1.products.show', $product))
            ->assertOk()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'slug',
                    'description',
                    'unit_price',
                ],
                'message',
            ]);
    }

    /**
     * Test that an admin can create a product.
     */
    public function test_admin_can_create_product(): void
    {
        $admin = User::factory()->create(['user_type' => 'admin']);
        
        $productData = [
            'name' => 'Test Product',
            'category_ids' => [1],
            'unit' => 'pcs',
            'min_qty' => 1,
            'tags' => 'test,product',
            'description' => 'This is a test product',
            'unit_price' => 100,
            'thumbnail_img' => 'test.jpg',
            'photos' => json_encode(['test.jpg']),
        ];

        $this
            ->actingAs($admin)
            ->postJson(route('api.v1.products.store'), $productData)
            ->assertCreated()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'slug',
                ],
                'message',
            ]);

        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'unit' => 'pcs',
        ]);
    }

    /**
     * Test that an admin can update a product.
     */
    public function test_admin_can_update_product(): void
    {
        $admin = User::factory()->create(['user_type' => 'admin']);
        $product = Product::factory()->create();
        
        $updateData = [
            'name' => 'Updated Product Name',
            'description' => 'Updated product description',
        ];

        $this
            ->actingAs($admin)
            ->patchJson(route('api.v1.products.update', $product), $updateData)
            ->assertOk()
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'name',
                    'slug',
                ],
                'message',
            ]);

        $this->assertDatabaseHas('products', [
            'id' => $product->id,
            'name' => 'Updated Product Name',
            'description' => 'Updated product description',
        ]);
    }

    /**
     * Test that an admin can delete a product.
     */
    public function test_admin_can_delete_product(): void
    {
        $admin = User::factory()->create(['user_type' => 'admin']);
        $product = Product::factory()->create();

        $this
            ->actingAs($admin)
            ->deleteJson(route('api.v1.products.destroy', $product))
            ->assertOk()
            ->assertJsonStructure([
                'success',
                'message',
            ]);

        $this->assertDatabaseMissing('products', [
            'id' => $product->id,
        ]);
    }
}