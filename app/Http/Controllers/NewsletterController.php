<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Subscriber;
use Mail;
use App\Mail\EmailManager;

class NewsletterController extends Controller
{
    public function __construct() {
        // Staff Permission Check
        $this->middleware(['permission:send_newsletter'])->only('index');
    }

    public function index(Request $request)
    {
        $users = User::all();
        $subscribers = Subscriber::all();
        return view('backend.marketing.newsletters.index', compact('users', 'subscribers'));
    }

    public function send(Request $request)
    {
        if (env('MAIL_USERNAME') != null) {
            //sends newsletter to selected users
        	if ($request->has('user_emails')) {
                foreach ($request->user_emails as $key => $email) {
                    $array['view'] = 'emails.newsletter';
                    $array['subject'] = $request->subject;
                    $array['from'] = env('MAIL_FROM_ADDRESS');
                    $array['content'] = $request->content;

                    try {
                        Mail::to($email)->queue(new EmailManager($array));
                    } catch (\Exception $e) {
                        //dd($e);
                    }
            	}
            }

            //sends newsletter to subscribers
            if ($request->has('subscriber_emails')) {
                foreach ($request->subscriber_emails as $key => $email) {
                    $array['view'] = 'emails.newsletter';
                    $array['subject'] = $request->subject;
                    $array['from'] = env('MAIL_FROM_ADDRESS');
                    $array['content'] = $request->content;

                    try {
                        Mail::to($email)->queue(new EmailManager($array));
                    } catch (\Exception $e) {
                        //dd($e);
                    }
            	}
            }
        }
        else {
            flash(translate('Please configure SMTP first'))->error();
            return back();
        }

    	flash(translate('Newsletter has been send'))->success();
    	return redirect()->route('admin.dashboard');
    }

    // public function testEmail(Request $request){
        
    //     $request->validate([
    //         'email' => 'required|email'
    //     ]);

    //     dd([
    //             'submitted_email' => $request->email,
    //             'auth_user_email' => auth()->user()->email,
    //             'all_request_data' => $request->all()
    //         ]);

    //     $array['view'] = 'emails.newsletter';
    //     $array['subject'] = "SMTP Test";
    //     $array['from'] = env('MAIL_FROM_ADDRESS');
    //     $array['content'] = "This is a test email.";

    //     try {
    //         Mail::to($request->email)->send(new EmailManager($array));
    //     } catch (\Exception $e) {
    //         dd($e);
    //     }

    //     flash(translate('An email has been sent.'))->success();
    //     return back();
    // }

    public function testEmail(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        auth()->user()->update(['email' => $request->email]);

        $array['view'] = 'emails.newsletter';
        $array['subject'] = "SMTP Test";
        $array['from'] = env('MAIL_FROM_ADDRESS');
        $array['content'] = "This is a test email sent to: " . $request->email;

        try {
         
            Mail::to($request->email)->send(new EmailManager($array));
            \Log::info("Test email sent successfully to: " . $request->email);
        } catch (\Exception $e) {
            \Log::error("Failed to send test email: " . $e->getMessage());
            flash(translate('Failed to send email. Please check your SMTP settings.'))->error();
            return back();
        }

        flash(translate('Test email sent successfully to: ' . $request->email))->success();
        return back();
    }


}
