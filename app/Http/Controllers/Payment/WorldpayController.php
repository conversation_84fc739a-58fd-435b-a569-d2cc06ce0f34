<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\CombinedOrder;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Session;


class WorldpayController extends Controller
{

    public function pay1(Request $request)
    {
        try {
            $username = env('WORLDPAY_USERNAME');
            $password = env('WORLDPAY_PASSWORD');
            $apiUrl = "https://try.access.worldpay.com/payment_pages";

            // Prepare the payload as an associative array (not a string)
            $payload = [
                "transactionReference" => "MyTransaction123",
                "merchant" => [
                    "entity" => "PO4071633825"
                ],
                "narrative" => [
                    "line1" => "tte"
                ],
                "value" => [
                    "currency" => "GBP",
                    "amount" => 123
                ],
                "description" => "Mind Palace Ltd",
                "billingAddressName" => "<PERSON> Holmes",
                "billingAddress" => [
                    "address1" => "221B Baker Street",
                    "address2" => "Marylebone",
                    "address3" => "Westminster",
                    "postalCode" => "NW1 6XE",
                    "city" => "London",
                    "state" => "Greater London",
                    "countryCode" => "GB"
                ],
                "resultURLs" => [
                    "successURL" => "https://mindpalace-website/result/success",
                    "pendingURL" => "https://mindpalace-website/result/pending",
                    "failureURL" => "https://mindpalace-website/result/failure",
                    "errorURL" => "https://mindpalace-website/result/error",
                    "cancelURL" => "https://mindpalace-website/result/cancel",
                    "expiryURL" => "https://mindpalace-website/result/expiry"
                ],
                "riskData" => [
                    "shipping" => [
                        "firstName" => "James",
                        "lastName" => "Moriarty",
                        "address" => [
                            "city" => "Durham",
                            "address1" => "The Palatine Centre",
                            "address2" => "Durham University",
                            "address3" => "Stockton Road",
                            "state" => "County Durham",
                            "countryCode" => "GB",
                            "postalCode" => "DH1 3LE",
                            "phoneNumber" => "01189998819999197253"
                        ],
                        "method" => "verifiedAddress",
                        "nameMatchesAccountName" => false,
                        "email" => "<EMAIL>",
                        "timeFrame" => "nextDay"
                    ],
                    "custom" => [
                        "string1" => "foo", "number1" => 1,
                        "string2" => "foo", "number2" => 1,
                        "string3" => "foo", "number3" => 1,
                        "string4" => "foo", "number4" => 1,
                        "string5" => "foo", "number5" => 1,
                        "string6" => "foo", "number6" => 1,
                        "string7" => "foo", "number7" => 1,
                        "string8" => "foo", "number8" => 1,
                        "string9" => "foo", "number9" => 1
                    ],
                    "account" => [
                        "dateOfBirth" => "1835-04-01",
                        "history" => [
                            "createdAt" => "1876-06-01",
                            "modifiedAt" => "1876-08-13",
                            "paymentAccountEnrolledAt" => "1876-06-01",
                            "passwordModifiedAt" => "1876-06-01"
                        ],
                        "type" => "fidoAuthenticator",
                        "previousSuspiciousActivity" => false,
                        "email" => "<EMAIL>"
                    ],
                    "transaction" => [
                        "firstName" => "James",
                        "lastName" => "Moriarty",
                        "phoneNumber" => "01189998819999197253",
                        "preOrderDate" => "1876-08-13",
                        "reorder" => false,
                        "history" => [
                            "attemptsLastYear" => 3,
                            "completedLastSixMonths" => 4,
                            "attemptsLastDay" => 2,
                            "shippingAddressFirstUsedAt" => "1876-06-01",
                            "addCardsLastDay" => 1
                        ],
                        "giftCardsPurchase" => [
                            "totalValue" => [
                                "amount" => 10000,
                                "currency" => "GBP"
                            ],
                            "quantity" => 1
                        ]
                    ]
                ],
                "expiry" => "600"
            ];

            $response = response()->json($payload);
            $jsonContent = $response->getContent();
            $decodedJson = json_decode($jsonContent, true);

            // Now, print the decoded JSON or log it
            echo "<pre>";
            print_r($decodedJson);
            echo "</pre>";
            exit();   // Set up Guzzle client

            $client = new Client();
            $response = $client->post($apiUrl, [
                'auth' => [$username, $password], // Basic Auth
                'headers' => [
                    'Content-Type' => 'application/vnd.worldpay.payment_pages-v1.hal+json', // Required Content-Type
                    'Accept' => 'application/json' // Accept JSON response
                ],
                'json' => $payload // Automatically converts payload to JSON
            ]);

            // Parse and handle the response
            $responseBody = json_decode($response->getBody(), true);

            if ($response->getStatusCode() === 200) {
                return redirect()->route('order.success')->with('message', 'Payment successful!');
            }

            return redirect()->route('order.failed')->withErrors('Payment failed: ' . ($responseBody['message'] ?? 'Unknown error'));
        } catch (\Exception $e) {
            // Handle exception
            print_r($e->getMessage());
            exit();
            return redirect()->route('order.failed')->withErrors('Error: ' . $e->getMessage());
        }
    }

    public function pay()
    {
        $combined_order = CombinedOrder::findOrFail(Session::get('combined_order_id'));

        $shipping_address = json_decode($combined_order->shipping_address, true);

        $transactionReference = 'TXN-' . $combined_order->id . '-' . time();

        $url = 'https://try.access.worldpay.com/payment_pages';

        $payload = [
            "transactionReference" => $transactionReference,
            "merchant" => [
                "entity" => "PO4071633825"
            ],
            "narrative" => [
                "line1" => "tte"
            ],
            "value" => [
                "currency" => "GBP",
                "amount" => $combined_order->grand_total * 100,
            ],
            "description" => "Mind Palace Ltd",
            "billingAddress" => [
                "address1" => "123 Test Street",
                "address2" => "Apt 1", // Optional
                "address3" => "",      // Optional
                "postalCode" => "SW1A 1AA", // Valid UK postal code
                "city" => "London",
                "state" => "London",
                "countryCode" => "GB" // GB for the United Kingdom
            ],
            "billingAddressName" => "John Doe",
            "resultURLs" => [
                "successURL" => route('worldpay/success/callback'),
                "failureURL" => route('worldpay/failed/callback'),
            ],
            "expiry" => "600"
        ];

        $username = env('WORLDPAY_USERNAME');
        $password = env('WORLDPAY_PASSWORD');

        $response = Http::withBasicAuth($username, $password)
            ->withHeaders([
                'Content-Type' => 'application/vnd.worldpay.payment_pages-v1.hal+json'
            ])
            ->post($url, $payload);

        if ($response->successful()) {
            $response = response()->json($response->json());
            $data = $response->getData(true);
            if (isset($data['url'])) {
                return redirect($data['url']); // Redirect to the URL
            }
        } else {
            return response()->json([
                'error' => 'Request failed',
                'details' => $response->body()
            ], 400);
        }
    }

    public function worldPaySuccessCallback(Request $request)
    {
        $paymentID = $request->query('paymentID');
        print_r($paymentID);
        exit();
    }

    public function worldPayFailedCallback(Request $request)
    {
        print_r($_POST);
        exit();
    }

    public function handleSuccess(Request $request)
    {
        Log::info('Formatted Payment Details 1:', ['payment_details' => $request->all()]);

        $transactionReference = $request->get('transactionReference')
            ?? ($request->input('eventDetails.transactionReference') ?? null);

        if (!$transactionReference) {
            return redirect()->route('order.failed')->with('error', 'Transaction reference missing');
        }

        $username = env('WORLDPAY_USERNAME');
        $password = env('WORLDPAY_PASSWORD');

        $url = 'https://try.access.worldpay.com/paymentQueries/payments?transactionReference=' . $transactionReference;

        try {

            $response = Http::withBasicAuth($username, $password)->get($url);

            // Log the response details
            Log::info('Response Body:', ['response_body' => $response->body()]);
            Log::info('Response Status:', ['status_code' => $response->status()]);

            // Check if the response is successful
            if ($response->ok()) {
                $paymentDetails = $response->json();

                // Check for the _embedded.payments array
                if (isset($paymentDetails['_embedded']['payments']) && !empty($paymentDetails['_embedded']['payments'])) {
                    $payment = $paymentDetails['_embedded']['payments'][0]; // Take the first payment object

                    // Ensure 'status' exists in the payment object
                    if (isset($payment['status'])) {
                        if ($payment['status'] === 'SUCCESS') {
                            return redirect()->route('order.success')->with('message', 'Payment successful');
                        } else {
                            return redirect()->route('order.failed')->with('error', 'Payment failed');
                        }
                    } else {
                        Log::error('Missing "status" in payment object', ['payment' => $payment]);
                        return redirect()->route('order.failed')->with('error', 'Invalid payment details in response');
                    }
                } else {
                    Log::error('No payments found in response', ['payment_details' => $paymentDetails]);
                    return redirect()->route('order.failed')->with('error', 'No payment details found');
                }
            } else {
                // Log error for unsuccessful API response
                Log::error('Failed to retrieve payment details', ['response_body' => $response->body()]);
                return redirect()->route('order.failed')->with('error', 'Failed to retrieve payment details');
            }
        } catch (\Exception $e) {
            // Catch any exceptions and log them
            Log::error('Exception occurred while handling payment', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('order.failed')->with('error', 'An error occurred while processing the payment');
        }
    }


}
