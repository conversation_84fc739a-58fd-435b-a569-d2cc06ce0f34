<?php

namespace App\Http\Requests\Api\V2;

use Illuminate\Foundation\Http\FormRequest;

class ProductSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'categories' => 'nullable|string',
            'brands' => 'nullable|string',
            'sort_key' => 'nullable|string|in:price_low_to_high,price_high_to_low,new_arrival,popularity,top_rated',
            'name' => 'nullable|string|max:255',
            'min' => 'nullable|numeric',
            'max' => 'nullable|numeric',
        ];
    }
}