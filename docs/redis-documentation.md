# Redis Usage in the Project

## Overview
This document outlines how Redis is configured and used in the e-commerce application. Redis is an open-source, in-memory data structure store that can be used as a database, cache, and message broker.

## Configuration

### Redis Connection Configuration
Redis is configured in `config/database.php` with the following settings:

```php
'redis' => [
    'client' => 'predis',
    'default' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD', null),
        'port' => env('REDIS_PORT', 6379),
        'database' => env('REDIS_DB', 0),
    ],
    'cache' => [
        'host' => env('REDIS_HOST', '127.0.0.1'),
        'password' => env('REDIS_PASSWORD', null),
        'port' => env('REDIS_PORT', 6379),
        'database' => env('REDIS_CACHE_DB', 1),
    ],
],
```

The application uses the Predis client for Redis connections. There are two Redis connections configured:
1. `default` - Uses database 0 (configurable via REDIS_DB env variable)
2. `cache` - Uses database 1 (configurable via REDIS_CACHE_DB env variable)

### Cache Configuration
Redis can be used as a cache driver, configured in `config/cache.php`:

```php
'redis' => [
    'driver' => 'redis',
    'connection' => 'cache',
],
```

The default cache driver is set to 'file' (from the CACHE_DRIVER environment variable), but Redis is available as an option. To use Redis for caching, set the CACHE_DRIVER environment variable to 'redis'.

### Queue Configuration
Redis can be used as a queue driver, configured in `config/queue.php`:

```php
'redis' => [
    'driver' => 'redis',
    'connection' => 'default',
    'queue' => 'default',
    'retry_after' => 90,
    'block_for' => null,
],
```

The default queue driver is set to 'sync' (from the QUEUE_CONNECTION environment variable), but Redis is available as an option. To use Redis for queues, set the QUEUE_CONNECTION environment variable to 'redis'.

### Broadcasting Configuration
Redis can be used as a broadcasting driver, configured in `config/broadcasting.php`:

```php
'redis' => [
    'driver' => 'redis',
    'connection' => 'default',
],
```

The default broadcasting driver is set to 'null' (from the BROADCAST_DRIVER environment variable), but Redis is available as an option. To use Redis for broadcasting, set the BROADCAST_DRIVER environment variable to 'redis'.

## Usage in the Application

### Caching
The application uses caching in several places, particularly in the `ProductController` to cache product-related data:

1. **Product Details Caching**
   ```php
   // app/Http/Controllers/Api/V2/ProductController.php
   public function product_details($slug, $user_id)
   {
       $product = Cache::remember('product_details_'.$slug, 3600, function () use ($slug) {
           return Product::where('slug', $slug)->get();
       });
       
       // ...
   }
   ```
   This caches product details by slug for 3600 seconds (1 hour).

2. **Today's Deal Products Caching**
   ```php
   // app/Http/Controllers/Api/V2/ProductController.php
   public function todaysDeal()
   {
       return Cache::remember('todays_deal_products', 3600, function () {
           $products = Product::where('todays_deal', 1)->physical();
           return new ProductMiniCollection(filter_products($products)->limit(20)->get());
       });
   }
   ```
   This caches today's deal products for 3600 seconds (1 hour).

3. **Flash Deals Caching**
   ```php
   // app/Http/Controllers/Api/V2/ProductController.php
   public function flashDeal()
   {
       return Cache::remember('app.flash_deals', 86400, function () {
           $flash_deals = FlashDeal::where('status', 1)->where('featured', 1)
               ->where('start_date', '<=', strtotime(date('d-m-Y')))
               ->where('end_date', '>=', strtotime(date('d-m-Y')))
               ->get();
           return new FlashDealCollection($flash_deals);
       });
   }
   ```
   This caches flash deals for 86400 seconds (24 hours).

4. **Featured Products Caching**
   ```php
   // app/Http/Controllers/Api/V2/ProductController.php
   public function featured()
   {
       return Cache::remember('featured_products', 3600, function () {
           $products = Product::where('featured', 1)->physical();
           return new ProductMiniCollection(filter_products($products)->latest()->limit(20)->get());
       });
   }
   ```
   This caches featured products for 3600 seconds (1 hour).

5. **Best Seller Products Caching**
   ```php
   // app/Http/Controllers/Api/V2/ProductController.php
   public function bestSeller()
   {
       return Cache::remember('best_seller_products', 3600, function () {
           $products = Product::orderBy('num_of_sale', 'desc')->physical();
           return new ProductMiniCollection(filter_products($products)->limit(20)->get());
       });
   }
   ```
   This caches best seller products for 3600 seconds (1 hour).

### Queues
The application has Redis configured as a queue driver option, but there are no job classes or queue dispatches found in the codebase. This suggests that the application is not currently using queues for background processing.

### Broadcasting
The application has Redis configured as a broadcasting driver option, but there's no evidence of broadcasting being used in the application.

## Conclusion
Redis is primarily used in this application for caching product-related data to improve performance. The application is configured to use Redis for queues and broadcasting as well, but these features don't appear to be actively used in the current codebase.

To enable Redis for caching, set the CACHE_DRIVER environment variable to 'redis'. Similarly, to use Redis for queues or broadcasting, set the QUEUE_CONNECTION or BROADCAST_DRIVER environment variables to 'redis', respectively.