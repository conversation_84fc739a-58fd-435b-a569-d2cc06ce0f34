{"private": true, "scripts": {"dev": "pnpm run development", "development": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider node_modules/.bin/webpack --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "pnpm run development -- --watch", "watch-poll": "pnpm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider node_modules/.bin/webpack --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "pnpm run production", "production": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider node_modules/.bin/webpack --no-progress --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@vue/compiler-sfc": "^3.5.17", "axios": "1.10.0", "babel-loader": "10.0.0", "bootstrap": "5.3.7", "cross-env": "5.2.1", "jquery": "3.7.1", "laravel-mix": "6.0.49", "lodash": "4.17.21", "popper.js": "1.16.1", "sass": "1.89.2", "sass-loader": "16.0.5", "vue": "3.5.17", "vue-loader": "^16.8.3", "webpack": "5.100.1"}}