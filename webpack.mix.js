const mix = require('laravel-mix');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */

// Temporarily disable sass processing to avoid node-sass compatibility issues
// with arm64 architecture and Node.js v22.12.0
mix.js('resources/js/app.js', 'public/js')
   .vue({ version: 3 });

// Copy an empty CSS file to public/css to avoid 404 errors
mix.copy('resources/sass/app.scss', 'public/css/app.css');
